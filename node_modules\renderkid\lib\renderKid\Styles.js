"use strict";

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

// Generated by CoffeeScript 2.5.1
var MixedDeclarationSet, StyleSheet, Styles, terminalWidth;
StyleSheet = require('./styles/StyleSheet');
MixedDeclarationSet = require('./styles/rule/MixedDeclarationSet');
terminalWidth = require('../tools').getCols();

module.exports = Styles = function () {
  var self;

  var Styles = /*#__PURE__*/function () {
    function Styles() {
      _classCallCheck(this, Styles);

      this._defaultStyles = new StyleSheet();
      this._userStyles = new StyleSheet();

      this._setDefaultStyles();
    }

    _createClass(Styles, [{
      key: "_setDefaultStyles",
      value: function _setDefaultStyles() {
        this._defaultStyles.setRule(self.defaultRules);
      }
    }, {
      key: "setRule",
      value: function setRule(selector, rules) {
        this._userStyles.setRule.apply(this._userStyles, arguments);

        return this;
      }
    }, {
      key: "getStyleFor",
      value: function getStyleFor(el) {
        var styles;
        styles = el.styles;

        if (styles == null) {
          el.styles = styles = this._getComputedStyleFor(el);
        }

        return styles;
      }
    }, {
      key: "_getRawStyleFor",
      value: function _getRawStyleFor(el) {
        var def, user;
        def = this._defaultStyles.getRulesFor(el);
        user = this._userStyles.getRulesFor(el);
        return MixedDeclarationSet.mix(def, user).toObject();
      }
    }, {
      key: "_getComputedStyleFor",
      value: function _getComputedStyleFor(el) {
        var decs, parent, prop, ref, val;
        decs = {};
        parent = el.parent;
        ref = this._getRawStyleFor(el);

        for (prop in ref) {
          val = ref[prop];

          if (val !== 'inherit') {
            decs[prop] = val;
          } else {
            throw Error("Inherited styles are not supported yet.");
          }
        }

        return decs;
      }
    }]);

    return Styles;
  }();

  ;
  self = Styles;
  Styles.defaultRules = {
    '*': {
      display: 'inline'
    },
    'body': {
      background: 'none',
      color: 'white',
      display: 'block',
      width: terminalWidth + ' !important'
    }
  };
  return Styles;
}.call(void 0);